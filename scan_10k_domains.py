#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扫描1万个5字母.com域名可用性
使用多线程和优化的检查方法
"""

import whois
import itertools
import random
import time
import csv
import json
from datetime import datetime, timedelta
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import socket

class MassiveDomainScanner:
    def __init__(self, target_scan=10000, target_find=1000, max_workers=30):
        self.target_scan = target_scan
        self.target_find = target_find
        self.max_workers = max_workers
        self.found_domains = []
        self.checked_count = 0
        self.start_time = datetime.now()
        self.results_file = f"scan_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.csv_file = f"scan_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.progress_file = "massive_scan_progress.json"
        self.lock = threading.Lock()
        self.error_count = 0
        
    def generate_smart_combinations(self):
        """智能生成域名组合，优先生成高成功率的模式"""
        letters = 'abcdefghijklmnopqrstuvwxyz'
        domains = []
        
        print("生成高成功率模式域名...")
        
        # 模式1: 包含不常见字母的组合 (30%)
        uncommon_letters = ['q', 'x', 'z', 'j', 'k', 'v', 'w']
        for _ in range(self.target_scan // 3):
            domain = ''.join([
                random.choice(uncommon_letters) if random.random() < 0.6 else random.choice(letters),
                random.choice(letters),
                random.choice(letters),
                random.choice(uncommon_letters) if random.random() < 0.4 else random.choice(letters),
                random.choice(letters)
            ])
            domains.append(domain)
        
        # 模式2: 重复字母模式 (25%)
        for _ in range(self.target_scan // 4):
            repeat_letter = random.choice(letters)
            other_letters = random.choices([c for c in letters if c != repeat_letter], k=3)
            domain_chars = [repeat_letter, repeat_letter] + other_letters
            random.shuffle(domain_chars)
            domains.append(''.join(domain_chars))
        
        # 模式3: 辅音密集 (25%)
        consonants = [c for c in letters if c not in 'aeiou']
        for _ in range(self.target_scan // 4):
            consonant_count = random.randint(3, 4)
            vowel_count = 5 - consonant_count
            domain_chars = (random.choices(consonants, k=consonant_count) + 
                          random.choices('aeiou', k=vowel_count))
            random.shuffle(domain_chars)
            domains.append(''.join(domain_chars))
        
        # 模式4: 完全随机但避免常见模式 (20%)
        while len(domains) < self.target_scan:
            domain = ''.join(random.choices(letters, k=5))
            # 避免太多元音连续
            vowel_count = sum(1 for c in domain if c in 'aeiou')
            if vowel_count <= 2:
                domains.append(domain)
        
        # 去重并打乱
        domains = list(set(domains))
        random.shuffle(domains)
        return domains[:self.target_scan]
    
    def quick_domain_check(self, domain):
        """快速域名检查 - 多层验证"""
        try:
            # 第一层：DNS解析检查
            try:
                socket.gethostbyname(domain)
                return False  # 能解析说明已注册
            except socket.gaierror:
                pass  # 解析失败，继续下一步检查
            
            # 第二层：whois查询（更准确但较慢）
            try:
                w = whois.whois(domain)
                if w.domain_name is not None:
                    return False
                else:
                    return True
            except whois.parser.PywhoisError:
                return True  # whois查询失败通常表示未注册
            except Exception:
                return False  # 其他错误保守判断为已注册
                
        except Exception as e:
            with self.lock:
                self.error_count += 1
            return False
    
    def check_domain_batch(self, combo):
        """检查单个域名"""
        domain = f"{combo}.com"
        
        if self.quick_domain_check(domain):
            with self.lock:
                self.found_domains.append(combo)
                print(f"\n✅ 找到可注册域名: {domain} (总计: {len(self.found_domains)})")
                return True
        
        return False
    
    def save_progress(self):
        """保存进度"""
        progress_data = {
            'found_domains': self.found_domains,
            'checked_count': self.checked_count,
            'error_count': self.error_count,
            'start_time': self.start_time.isoformat(),
            'target_scan': self.target_scan,
            'target_find': self.target_find
        }
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
    
    def load_progress(self):
        """加载之前的进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    self.found_domains = progress_data.get('found_domains', [])
                    self.checked_count = progress_data.get('checked_count', 0)
                    self.error_count = progress_data.get('error_count', 0)
                    print(f"加载进度：已找到 {len(self.found_domains)} 个域名，已检查 {self.checked_count} 个")
                    return True
            except Exception as e:
                print(f"加载进度失败: {e}")
        return False
    
    def save_results(self):
        """保存结果到文件"""
        # 保存到TXT文件
        with open(self.results_file, 'w', encoding='utf-8') as f:
            f.write(f"# 大规模域名扫描结果\n")
            f.write(f"# 扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 扫描总数: {self.checked_count} 个域名\n")
            f.write(f"# 找到可注册: {len(self.found_domains)} 个域名\n")
            f.write(f"# 成功率: {(len(self.found_domains)/self.checked_count*100):.2f}%\n")
            f.write(f"# 错误数量: {self.error_count}\n\n")
            
            for i, domain in enumerate(self.found_domains, 1):
                f.write(f"{i:4d}. {domain}.com\n")
        
        # 保存到CSV文件
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['序号', '域名', '完整域名', '发现时间'])
            for i, domain in enumerate(self.found_domains, 1):
                writer.writerow([i, domain, f"{domain}.com", datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    
    def print_progress(self):
        """显示进度信息"""
        elapsed = datetime.now() - self.start_time
        found_count = len(self.found_domains)
        if self.checked_count > 0:
            success_rate = (found_count / self.checked_count) * 100
            speed = self.checked_count / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0
            eta_seconds = (self.target_scan - self.checked_count) / speed if speed > 0 else 0
            eta = str(timedelta(seconds=int(eta_seconds)))
        else:
            success_rate = 0
            speed = 0
            eta = "未知"
        
        print(f"\r进度: 已找到 {found_count} | "
              f"已扫描 {self.checked_count}/{self.target_scan} | "
              f"成功率 {success_rate:.2f}% | "
              f"速度 {speed:.1f}/秒 | "
              f"错误 {self.error_count} | "
              f"预计剩余 {eta}", end='', flush=True)
    
    def scan_domains(self):
        """主要的域名扫描函数"""
        print("=== 开始大规模域名扫描 ===")
        print(f"目标扫描: {self.target_scan} 个域名")
        print(f"目标找到: {self.target_find} 个可注册域名")
        print(f"使用线程: {self.max_workers} 个")
        
        # 尝试加载之前的进度
        self.load_progress()
        
        if len(self.found_domains) >= self.target_find:
            print(f"\n已达到目标！找到 {len(self.found_domains)} 个可注册域名")
            self.save_results()
            return
        
        # 生成域名组合
        print("生成智能域名组合...")
        combinations = self.generate_smart_combinations()
        print(f"生成了 {len(combinations)} 个域名组合")
        
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_combo = {
                    executor.submit(self.check_domain_batch, combo): combo 
                    for combo in combinations[self.checked_count:]
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_combo):
                    if self.checked_count >= self.target_scan or len(self.found_domains) >= self.target_find:
                        break
                    
                    self.checked_count += 1
                    
                    # 每检查100个域名显示一次进度
                    if self.checked_count % 100 == 0:
                        self.print_progress()
                        self.save_progress()
                    
                    # 每找到50个域名暂停一下
                    if len(self.found_domains) % 50 == 0 and len(self.found_domains) > 0:
                        time.sleep(1)
                        
        except KeyboardInterrupt:
            print(f"\n\n用户中断，已扫描 {self.checked_count} 个域名")
        
        print(f"\n\n扫描完成！")
        print(f"扫描了 {self.checked_count} 个域名")
        print(f"找到 {len(self.found_domains)} 个可注册域名")
        print(f"成功率: {(len(self.found_domains)/self.checked_count*100):.2f}%")
        print(f"错误数量: {self.error_count}")
        
        # 保存结果
        self.save_results()
        print(f"结果已保存到: {self.results_file} 和 {self.csv_file}")

def main():
    """主函数"""
    print("=== 大规模5字母.com域名扫描工具 ===")
    
    # 获取参数
    target_scan = 10000
    target_find = 1000
    
    if len(sys.argv) > 1:
        try:
            target_scan = int(sys.argv[1])
        except ValueError:
            print("无效的扫描数量，使用默认值10000")
    
    if len(sys.argv) > 2:
        try:
            target_find = int(sys.argv[2])
        except ValueError:
            print("无效的目标数量，使用默认值1000")
    
    scanner = MassiveDomainScanner(
        target_scan=target_scan, 
        target_find=target_find, 
        max_workers=50
    )
    scanner.scan_domains()

if __name__ == "__main__":
    main()
