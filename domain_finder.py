#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5字母.com域名查找工具
生成并检查未注册的5个小写字母组成的.com域名
"""

import whois
import itertools
import random
import time
import csv
import json
from datetime import datetime
import sys
import os

class DomainFinder:
    def __init__(self, target_count=1000):
        self.target_count = target_count
        self.found_domains = []
        self.checked_count = 0
        self.start_time = datetime.now()
        self.results_file = f"available_domains_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.csv_file = f"available_domains_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.progress_file = "domain_finder_progress.json"
        
    def generate_5_letter_combinations(self):
        """生成所有5个小写字母的组合"""
        letters = 'abcdefghijklmnopqrstuvwxyz'
        combinations = [''.join(combo) for combo in itertools.product(letters, repeat=5)]
        random.shuffle(combinations)  # 随机打乱顺序
        return combinations
    
    def is_domain_available(self, domain):
        """检查域名是否可注册"""
        try:
            # 查询域名whois信息
            w = whois.whois(domain)
            
            # 如果域名已注册，whois会返回注册信息
            if w.domain_name is not None:
                return False
            else:
                return True
                
        except whois.parser.PywhoisError:
            # 如果查询出错，通常表示域名未注册
            return True
        except Exception as e:
            print(f"查询 {domain} 时出错: {e}")
            # 出错时返回False，避免误判
            return False
    
    def save_progress(self):
        """保存进度"""
        progress_data = {
            'found_domains': self.found_domains,
            'checked_count': self.checked_count,
            'start_time': self.start_time.isoformat(),
            'target_count': self.target_count
        }
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
    
    def load_progress(self):
        """加载之前的进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    self.found_domains = progress_data.get('found_domains', [])
                    self.checked_count = progress_data.get('checked_count', 0)
                    self.target_count = progress_data.get('target_count', 1000)
                    print(f"加载进度：已找到 {len(self.found_domains)} 个域名，已检查 {self.checked_count} 个")
                    return True
            except Exception as e:
                print(f"加载进度失败: {e}")
        return False
    
    def save_results(self):
        """保存结果到文件"""
        # 保存到TXT文件
        with open(self.results_file, 'w', encoding='utf-8') as f:
            f.write(f"# 可注册的5字母.com域名列表\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 总计: {len(self.found_domains)} 个域名\n\n")
            for domain in self.found_domains:
                f.write(f"{domain}.com\n")
        
        # 保存到CSV文件
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['域名', '完整域名', '检查时间'])
            for domain in self.found_domains:
                writer.writerow([domain, f"{domain}.com", datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    
    def print_progress(self):
        """显示进度信息"""
        elapsed = datetime.now() - self.start_time
        found_count = len(self.found_domains)
        if self.checked_count > 0:
            success_rate = (found_count / self.checked_count) * 100
            estimated_total = int(self.target_count / (found_count / self.checked_count)) if found_count > 0 else 0
            remaining = estimated_total - self.checked_count if estimated_total > self.checked_count else 0
        else:
            success_rate = 0
            remaining = 0
        
        print(f"\r进度: 已找到 {found_count}/{self.target_count} | "
              f"已检查 {self.checked_count} | "
              f"成功率 {success_rate:.2f}% | "
              f"用时 {elapsed} | "
              f"预计剩余 {remaining}", end='', flush=True)
    
    def find_domains(self):
        """主要的域名查找函数"""
        print("开始查找可注册的5字母.com域名...")
        print(f"目标: {self.target_count} 个域名")
        
        # 尝试加载之前的进度
        self.load_progress()
        
        if len(self.found_domains) >= self.target_count:
            print(f"\n已达到目标！找到 {len(self.found_domains)} 个可注册域名")
            self.save_results()
            return
        
        # 生成域名组合
        print("生成域名组合中...")
        combinations = self.generate_5_letter_combinations()
        print(f"总共有 {len(combinations)} 种组合")
        
        try:
            for combo in combinations:
                if len(self.found_domains) >= self.target_count:
                    break
                
                domain = f"{combo}.com"
                self.checked_count += 1
                
                if self.is_domain_available(domain):
                    self.found_domains.append(combo)
                    print(f"\n✅ 找到可注册域名: {domain}")
                
                # 每检查10个域名显示一次进度
                if self.checked_count % 10 == 0:
                    self.print_progress()
                    self.save_progress()  # 定期保存进度
                
                # 添加延迟避免被限制
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            print(f"\n\n用户中断，已找到 {len(self.found_domains)} 个域名")
        
        print(f"\n\n查找完成！")
        print(f"总共找到 {len(self.found_domains)} 个可注册的5字母.com域名")
        print(f"检查了 {self.checked_count} 个域名")
        
        # 保存结果
        self.save_results()
        print(f"结果已保存到: {self.results_file} 和 {self.csv_file}")

def main():
    """主函数"""
    print("=== 5字母.com域名查找工具 ===")
    
    # 获取目标数量
    target = 1000
    if len(sys.argv) > 1:
        try:
            target = int(sys.argv[1])
        except ValueError:
            print("无效的目标数量，使用默认值1000")
    
    finder = DomainFinder(target_count=target)
    finder.find_domains()

if __name__ == "__main__":
    main()
