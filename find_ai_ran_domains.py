#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找包含"ai"或"ran"的5字母.com域名
生成有意义的域名组合并检查可用性
"""

import whois
import random
import time
import csv
import json
from datetime import datetime
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class AIRanDomainFinder:
    def __init__(self, target_count=200, max_workers=20):
        self.target_count = target_count
        self.max_workers = max_workers
        self.found_domains = []
        self.checked_count = 0
        self.start_time = datetime.now()
        self.results_file = f"ai_ran_domains_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.csv_file = f"ai_ran_domains_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.progress_file = "ai_ran_progress.json"
        self.lock = threading.Lock()
        
    def generate_ai_domains(self):
        """生成包含'ai'的5字母域名"""
        letters = 'abcdefghijklmnopqrstuvwxyz'
        ai_domains = []
        
        # ai在开头: ai + 3个字母
        for c1 in letters:
            for c2 in letters:
                for c3 in letters:
                    ai_domains.append(f"ai{c1}{c2}{c3}")
        
        # ai在中间: 1个字母 + ai + 2个字母
        for c1 in letters:
            for c2 in letters:
                for c3 in letters:
                    ai_domains.append(f"{c1}ai{c2}{c3}")
        
        # ai在末尾: 3个字母 + ai
        for c1 in letters:
            for c2 in letters:
                for c3 in letters:
                    ai_domains.append(f"{c1}{c2}{c3}ai")
        
        return ai_domains
    
    def generate_ran_domains(self):
        """生成包含'ran'的5字母域名"""
        letters = 'abcdefghijklmnopqrstuvwxyz'
        ran_domains = []
        
        # ran在开头: ran + 2个字母
        for c1 in letters:
            for c2 in letters:
                ran_domains.append(f"ran{c1}{c2}")
        
        # ran在末尾: 2个字母 + ran
        for c1 in letters:
            for c2 in letters:
                ran_domains.append(f"{c1}{c2}ran")
        
        return ran_domains
    
    def generate_meaningful_combinations(self):
        """生成有意义的组合"""
        meaningful_domains = []
        
        # AI相关的有意义组合
        ai_prefixes = ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'v', 'w', 'x', 'y', 'z']
        ai_suffixes = ['bo', 'co', 'do', 'go', 'ho', 'jo', 'ko', 'lo', 'mo', 'no', 'po', 'ro', 'so', 'to', 'vo', 'wo', 'yo', 'zo',
                      'by', 'cy', 'dy', 'fy', 'gy', 'hy', 'jy', 'ky', 'ly', 'my', 'ny', 'py', 'ry', 'sy', 'ty', 'vy', 'wy', 'zy']
        
        # 生成 ai + 有意义后缀
        for suffix in ai_suffixes:
            meaningful_domains.append(f"ai{suffix}")
        
        # 生成 前缀 + ai
        for prefix in ai_prefixes:
            for suffix in ['x', 'y', 'z', 'o']:
                meaningful_domains.append(f"{prefix}ai{suffix}")
        
        # RAN相关的有意义组合
        ran_prefixes = ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 's', 't', 'v', 'w', 'x', 'y', 'z']
        ran_suffixes = ['a', 'e', 'i', 'o', 'u', 'y']
        
        # 生成 ran + 后缀
        for suffix in ran_suffixes:
            meaningful_domains.append(f"ran{suffix}")
        
        # 生成 前缀 + ran
        for prefix in ran_prefixes:
            meaningful_domains.append(f"{prefix}ran")
        
        return meaningful_domains
    
    def is_domain_available(self, domain):
        """检查域名是否可注册"""
        try:
            w = whois.whois(domain)
            if w.domain_name is not None:
                return False
            else:
                return True
        except whois.parser.PywhoisError:
            return True  # whois查询失败通常表示未注册
        except Exception as e:
            print(f"查询 {domain} 时出错: {e}")
            return False
    
    def check_domain(self, domain_name):
        """检查单个域名"""
        domain = f"{domain_name}.com"
        
        if self.is_domain_available(domain):
            with self.lock:
                self.found_domains.append(domain_name)
                pattern = "包含ai" if "ai" in domain_name else "包含ran"
                print(f"\n✅ 找到可注册域名: {domain} ({pattern}) (总计: {len(self.found_domains)})")
                return True
        
        return False
    
    def save_progress(self):
        """保存进度"""
        progress_data = {
            'found_domains': self.found_domains,
            'checked_count': self.checked_count,
            'start_time': self.start_time.isoformat(),
            'target_count': self.target_count
        }
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
    
    def load_progress(self):
        """加载之前的进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    self.found_domains = progress_data.get('found_domains', [])
                    self.checked_count = progress_data.get('checked_count', 0)
                    print(f"加载进度：已找到 {len(self.found_domains)} 个域名，已检查 {self.checked_count} 个")
                    return True
            except Exception as e:
                print(f"加载进度失败: {e}")
        return False
    
    def save_results(self):
        """保存结果到文件"""
        # 分类域名
        ai_domains = [d for d in self.found_domains if 'ai' in d]
        ran_domains = [d for d in self.found_domains if 'ran' in d]
        
        # 保存到TXT文件
        with open(self.results_file, 'w', encoding='utf-8') as f:
            f.write(f"# 包含'ai'或'ran'的5字母.com域名列表\n")
            f.write(f"# 查找时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 总计: {len(self.found_domains)} 个域名\n")
            f.write(f"# 包含'ai': {len(ai_domains)} 个\n")
            f.write(f"# 包含'ran': {len(ran_domains)} 个\n\n")
            
            if ai_domains:
                f.write("=== 包含'ai'的域名 ===\n")
                for i, domain in enumerate(ai_domains, 1):
                    f.write(f"{i:3d}. {domain}.com\n")
                f.write("\n")
            
            if ran_domains:
                f.write("=== 包含'ran'的域名 ===\n")
                for i, domain in enumerate(ran_domains, 1):
                    f.write(f"{i:3d}. {domain}.com\n")
        
        # 保存到CSV文件
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['序号', '域名', '完整域名', '包含模式', '位置', '发现时间'])
            for i, domain in enumerate(self.found_domains, 1):
                if 'ai' in domain:
                    pattern = "包含ai"
                    if domain.startswith('ai'):
                        position = "开头"
                    elif domain.endswith('ai'):
                        position = "结尾"
                    else:
                        position = "中间"
                else:
                    pattern = "包含ran"
                    if domain.startswith('ran'):
                        position = "开头"
                    elif domain.endswith('ran'):
                        position = "结尾"
                    else:
                        position = "中间"
                
                writer.writerow([i, domain, f"{domain}.com", pattern, position, datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    
    def print_progress(self):
        """显示进度信息"""
        elapsed = datetime.now() - self.start_time
        found_count = len(self.found_domains)
        if self.checked_count > 0:
            success_rate = (found_count / self.checked_count) * 100
            speed = self.checked_count / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0
        else:
            success_rate = 0
            speed = 0
        
        ai_count = len([d for d in self.found_domains if 'ai' in d])
        ran_count = len([d for d in self.found_domains if 'ran' in d])
        
        print(f"\r进度: 已找到 {found_count}/{self.target_count} (ai:{ai_count}, ran:{ran_count}) | "
              f"已检查 {self.checked_count} | "
              f"成功率 {success_rate:.2f}% | "
              f"速度 {speed:.1f}/秒", end='', flush=True)
    
    def find_domains(self):
        """主要的域名查找函数"""
        print("=== 查找包含'ai'或'ran'的5字母.com域名 ===")
        print(f"目标: {self.target_count} 个域名")
        
        # 尝试加载之前的进度
        self.load_progress()
        
        if len(self.found_domains) >= self.target_count:
            print(f"\n已达到目标！找到 {len(self.found_domains)} 个可注册域名")
            self.save_results()
            return
        
        # 生成域名组合
        print("生成域名组合...")
        
        # 优先生成有意义的组合
        meaningful_domains = self.generate_meaningful_combinations()
        print(f"生成了 {len(meaningful_domains)} 个有意义的组合")
        
        # 生成所有ai组合（采样）
        ai_domains = self.generate_ai_domains()
        ai_sample = random.sample(ai_domains, min(2000, len(ai_domains)))
        print(f"从 {len(ai_domains)} 个ai组合中采样 {len(ai_sample)} 个")
        
        # 生成所有ran组合
        ran_domains = self.generate_ran_domains()
        print(f"生成了 {len(ran_domains)} 个ran组合")
        
        # 合并并打乱
        all_domains = meaningful_domains + ai_sample + ran_domains
        random.shuffle(all_domains)
        
        print(f"总共准备检查 {len(all_domains)} 个域名")
        
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_domain = {
                    executor.submit(self.check_domain, domain): domain 
                    for domain in all_domains[self.checked_count:]
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_domain):
                    if len(self.found_domains) >= self.target_count:
                        break
                    
                    self.checked_count += 1
                    
                    # 每检查20个域名显示一次进度
                    if self.checked_count % 20 == 0:
                        self.print_progress()
                        self.save_progress()
                    
                    # 添加延迟避免被限制
                    time.sleep(0.2)
                        
        except KeyboardInterrupt:
            print(f"\n\n用户中断，已找到 {len(self.found_domains)} 个域名")
        
        print(f"\n\n查找完成！")
        ai_count = len([d for d in self.found_domains if 'ai' in d])
        ran_count = len([d for d in self.found_domains if 'ran' in d])
        print(f"总共找到 {len(self.found_domains)} 个可注册域名")
        print(f"包含'ai': {ai_count} 个")
        print(f"包含'ran': {ran_count} 个")
        print(f"检查了 {self.checked_count} 个域名")
        
        # 保存结果
        self.save_results()
        print(f"结果已保存到: {self.results_file} 和 {self.csv_file}")

def main():
    """主函数"""
    print("=== 包含'ai'或'ran'的域名查找工具 ===")
    
    # 获取目标数量
    target = 200
    if len(sys.argv) > 1:
        try:
            target = int(sys.argv[1])
        except ValueError:
            print("无效的目标数量，使用默认值200")
    
    finder = AIRanDomainFinder(target_count=target, max_workers=25)
    finder.find_domains()

if __name__ == "__main__":
    main()
