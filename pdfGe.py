from PyPDF2 import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import DecodedStreamObject, NameObject, ArrayObject

# === Configuration ===
input_path = "./Receipt-2764-0654_updated_email_vetede2419.pdf"
old_email = "<EMAIL>"
new_email = "<EMAIL>"
output_path = "./output.pdf"

# === Helper to generate the hex‐encoded representation used in PDFs ===
def to_hex_bytes(s: str) -> bytes:
    return b"<" + s.encode("utf-8").hex().encode("ascii") + b">"

hex_old = to_hex_bytes(old_email)
hex_new = to_hex_bytes(new_email)

reader = PdfReader(input_path)

# We’ll walk through every page and replace occurrences inside the content streams
for page in reader.pages:
    contents_obj = page["/Contents"]

    # Normalise to a list for uniform handling
    if isinstance(contents_obj, list):
        streams = [obj.get_object() for obj in contents_obj]
    else:
        streams = [contents_obj.get_object()]

    new_streams = []
    for stream in streams:
        data = stream.get_data()
        if hex_old in data or old_email.encode() in data:
            # Replace both possible raw and hex‑encoded versions
            data = data.replace(hex_old, hex_new)
            data = data.replace(old_email.encode(), new_email.encode())

            # Store in a *decoded* stream so we can write it back cleanly
            new_stream = DecodedStreamObject()
            new_stream.set_data(data)
            new_streams.append(new_stream)
        else:
            new_streams.append(stream)

    # Update the /Contents pointer on the page
    if len(new_streams) == 1:
        page[NameObject("/Contents")] = new_streams[0]
    else:
        page[NameObject("/Contents")] = ArrayObject(new_streams)

# === Write out the updated PDF ===
writer = PdfWriter()
for page in reader.pages:
    writer.add_page(page)

with open(output_path, "wb") as fh:
    writer.write(fh)

print('done', output_path)