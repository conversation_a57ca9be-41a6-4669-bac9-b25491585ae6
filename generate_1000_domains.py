#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成1000个5字母.com域名
基于已验证的高成功率模式生成更多域名
"""

import itertools
import random
import csv
from datetime import datetime

def generate_random_5_letter_domains(count=1000):
    """生成随机的5字母域名组合"""
    letters = 'abcdefghijklmnopqrstuvwxyz'
    domains = set()
    
    # 已知可注册的域名模式分析
    known_good = [
        "hfghx", "aoojh", "oczhh", "llddk", "lbfyu", "ljcgu", 
        "htppx", "rjons", "pboqt", "cjnyu", "spyws", "qyuho",
        "hhhzr", "ymrqb", "xqhde", "jjnpk", "qobjp", "cvdzx",
        "yukbd", "ijczc", "qvlei", "oyzes", "oaiwd", "qwsyd",
        "kckzl", "qrkbz", "aymms", "itfgv", "crnyo", "jtzse",
        "bwciy", "tpbzm", "fgvhv", "rcqav", "iwutq", "jnobx",
        "bwxdx", "dbbqv", "ydruc"
    ]
    
    # 添加已知的好域名
    for domain in known_good:
        domains.add(domain)
    
    # 分析模式：很多包含重复字母、不常见字母组合
    uncommon_letters = ['q', 'x', 'z', 'j', 'k', 'v', 'w', 'y']
    common_letters = ['a', 'e', 'i', 'o', 'u', 'r', 's', 't', 'n', 'l']
    
    while len(domains) < count:
        # 策略1: 包含不常见字母的组合
        if random.random() < 0.4:
            domain = ''.join([
                random.choice(uncommon_letters),
                random.choice(letters),
                random.choice(letters),
                random.choice(uncommon_letters),
                random.choice(letters)
            ])
        # 策略2: 包含重复字母
        elif random.random() < 0.3:
            base_letters = random.choices(letters, k=3)
            repeat_letter = random.choice(base_letters)
            domain = ''.join(base_letters + [repeat_letter, random.choice(letters)])
            random.shuffle(list(domain))
            domain = ''.join(domain)
        # 策略3: 避免常见单词模式
        else:
            domain = ''.join(random.choices(letters, k=5))
            # 避免太多元音连续
            vowels = 'aeiou'
            vowel_count = sum(1 for c in domain if c in vowels)
            if vowel_count > 3:
                continue
        
        # 避免明显的单词
        common_words = ['hello', 'world', 'admin', 'login', 'email', 'phone']
        if any(word in domain for word in common_words):
            continue
            
        domains.add(domain)
    
    return list(domains)

def save_domains_to_files(domains):
    """保存域名到文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    txt_file = f"1000_domains_{timestamp}.txt"
    csv_file = f"1000_domains_{timestamp}.csv"
    
    # 保存到TXT文件
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(f"# 1000个5字母.com域名列表\n")
        f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"# 总计: {len(domains)} 个域名\n")
        f.write(f"# 注意: 这些域名基于模式生成，需要实际验证是否可注册\n\n")
        
        for i, domain in enumerate(domains, 1):
            f.write(f"{i:4d}. {domain}.com\n")
    
    # 保存到CSV文件
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['序号', '域名', '完整域名', '生成时间'])
        for i, domain in enumerate(domains, 1):
            writer.writerow([i, domain, f"{domain}.com", datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    
    return txt_file, csv_file

def generate_pattern_based_domains(count=500):
    """基于成功模式生成域名"""
    domains = set()
    letters = 'abcdefghijklmnopqrstuvwxyz'
    
    # 模式1: 双字母重复 (如 llddk, hhhzr)
    for _ in range(count // 5):
        repeat_letter = random.choice(letters)
        other_letters = random.choices([c for c in letters if c != repeat_letter], k=3)
        domain_chars = [repeat_letter, repeat_letter] + other_letters
        random.shuffle(domain_chars)
        domains.add(''.join(domain_chars))
    
    # 模式2: 包含q但不跟u (如 qyuho, qobjp, qvlei)
    for _ in range(count // 5):
        pos = random.randint(0, 4)
        domain_chars = random.choices([c for c in letters if c != 'u'], k=5)
        domain_chars[pos] = 'q'
        domains.add(''.join(domain_chars))
    
    # 模式3: 包含x或z (如 cvdzx, bwxdx)
    for _ in range(count // 5):
        rare_letter = random.choice(['x', 'z', 'j', 'k'])
        pos = random.randint(0, 4)
        domain_chars = random.choices(letters, k=5)
        domain_chars[pos] = rare_letter
        domains.add(''.join(domain_chars))
    
    # 模式4: 辅音密集 (如 htppx, rcqav)
    consonants = [c for c in letters if c not in 'aeiou']
    for _ in range(count // 5):
        domain_chars = random.choices(consonants, k=4) + random.choices('aeiou', k=1)
        random.shuffle(domain_chars)
        domains.add(''.join(domain_chars))
    
    # 模式5: 随机但避免常见模式
    while len(domains) < count:
        domain = ''.join(random.choices(letters, k=5))
        # 避免太多元音
        vowel_count = sum(1 for c in domain if c in 'aeiou')
        if vowel_count <= 2:
            domains.add(domain)
    
    return list(domains)

def main():
    print("=== 生成1000个5字母.com域名 ===")
    
    # 生成基于模式的域名
    print("基于成功模式生成域名...")
    pattern_domains = generate_pattern_based_domains(500)
    
    # 生成随机域名
    print("生成随机域名...")
    random_domains = generate_random_5_letter_domains(500)
    
    # 合并并去重
    all_domains = list(set(pattern_domains + random_domains))
    
    # 如果不够1000个，补充更多
    if len(all_domains) < 1000:
        print(f"当前有 {len(all_domains)} 个域名，补充到1000个...")
        additional_needed = 1000 - len(all_domains)
        additional_domains = generate_random_5_letter_domains(additional_needed + 100)
        all_domains.extend([d for d in additional_domains if d not in all_domains])
    
    # 取前1000个
    final_domains = all_domains[:1000]
    
    print(f"生成完成！总共 {len(final_domains)} 个域名")
    
    # 保存到文件
    txt_file, csv_file = save_domains_to_files(final_domains)
    
    print(f"域名已保存到:")
    print(f"  TXT文件: {txt_file}")
    print(f"  CSV文件: {csv_file}")
    
    # 显示前20个域名作为示例
    print(f"\n前20个域名示例:")
    for i, domain in enumerate(final_domains[:20], 1):
        print(f"  {i:2d}. {domain}.com")
    
    print(f"\n注意: 这些域名基于成功模式生成，但仍需要实际验证是否可注册。")
    print(f"建议使用域名注册商的查询工具进行最终确认。")

if __name__ == "__main__":
    main()
