#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成10000个5字母.com域名
基于已验证的高成功率模式大规模生成
"""

import itertools
import random
import csv
from datetime import datetime

def generate_massive_domains(count=10000):
    """生成大量5字母域名组合"""
    letters = 'abcdefghijklmnopqrstuvwxyz'
    domains = set()
    
    # 已知可注册的域名（从之前的验证中获得）
    known_good = [
        "hfghx", "aoojh", "oczhh", "llddk", "lbfyu", "ljcgu", 
        "htppx", "rjons", "pboqt", "cjnyu", "spyws", "qyuho",
        "hhhzr", "ymrqb", "xqhde", "jjnpk", "qobjp", "cvdzx",
        "yukbd", "ijczc", "qvlei", "oyzes", "oaiwd", "qwsyd",
        "kckzl", "qrkbz", "aymms", "itfgv", "crnyo", "jtzse",
        "bwciy", "tpbzm", "fgvhv", "rcqav", "iwutq", "jnobx",
        "bwxdx", "dbbqv", "ydruc"
    ]
    
    # 添加已知的好域名
    for domain in known_good:
        domains.add(domain)
    
    print(f"添加了 {len(known_good)} 个已验证的域名")
    
    # 高成功率字母组合
    uncommon_letters = ['q', 'x', 'z', 'j', 'k', 'v', 'w', 'y']
    consonants = [c for c in letters if c not in 'aeiou']
    vowels = 'aeiou'
    
    # 策略1: 包含Q但不跟U的组合 (20%)
    print("生成Q系列域名...")
    target_q = count // 5
    while len([d for d in domains if 'q' in d]) < target_q and len(domains) < count:
        pos = random.randint(0, 4)
        domain_chars = random.choices([c for c in letters if c != 'u'], k=5)
        domain_chars[pos] = 'q'
        domain = ''.join(domain_chars)
        if 'qu' not in domain:  # 确保q不跟u
            domains.add(domain)
    
    # 策略2: 包含X或Z的组合 (20%)
    print("生成X/Z系列域名...")
    target_xz = count // 5
    while len([d for d in domains if 'x' in d or 'z' in d]) < target_xz and len(domains) < count:
        rare_letter = random.choice(['x', 'z'])
        pos = random.randint(0, 4)
        domain_chars = random.choices(letters, k=5)
        domain_chars[pos] = rare_letter
        domains.add(''.join(domain_chars))
    
    # 策略3: 双字母重复模式 (15%)
    print("生成重复字母域名...")
    target_repeat = count * 15 // 100
    while len([d for d in domains if any(d.count(c) >= 2 for c in d)]) < target_repeat and len(domains) < count:
        repeat_letter = random.choice(letters)
        other_letters = random.choices([c for c in letters if c != repeat_letter], k=3)
        domain_chars = [repeat_letter, repeat_letter] + other_letters
        random.shuffle(domain_chars)
        domains.add(''.join(domain_chars))
    
    # 策略4: 辅音密集组合 (15%)
    print("生成辅音密集域名...")
    target_consonant = count * 15 // 100
    while len([d for d in domains if sum(1 for c in d if c in consonants) >= 4]) < target_consonant and len(domains) < count:
        consonant_count = random.randint(4, 5)
        vowel_count = 5 - consonant_count
        domain_chars = (random.choices(consonants, k=consonant_count) + 
                      random.choices(vowels, k=vowel_count))
        random.shuffle(domain_chars)
        domains.add(''.join(domain_chars))
    
    # 策略5: 包含J/K/V/W的组合 (15%)
    print("生成J/K/V/W系列域名...")
    target_jkvw = count * 15 // 100
    while len([d for d in domains if any(c in d for c in 'jkvw')]) < target_jkvw and len(domains) < count:
        rare_letter = random.choice(['j', 'k', 'v', 'w'])
        pos = random.randint(0, 4)
        domain_chars = random.choices(letters, k=5)
        domain_chars[pos] = rare_letter
        domains.add(''.join(domain_chars))
    
    # 策略6: 避免常见模式的随机组合 (15%)
    print("生成随机避免常见模式域名...")
    common_patterns = ['admin', 'login', 'email', 'phone', 'hello', 'world', 'super', 'ultra']
    while len(domains) < count:
        domain = ''.join(random.choices(letters, k=5))
        
        # 避免太多元音
        vowel_count = sum(1 for c in domain if c in vowels)
        if vowel_count > 3:
            continue
            
        # 避免常见模式
        if any(pattern in domain for pattern in common_patterns):
            continue
            
        # 避免全是常见字母
        common_letters = 'erstnaiol'
        if all(c in common_letters for c in domain):
            continue
            
        domains.add(domain)
    
    return list(domains)

def save_domains_to_files(domains, prefix="10k_domains"):
    """保存域名到文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    txt_file = f"{prefix}_{timestamp}.txt"
    csv_file = f"{prefix}_{timestamp}.csv"
    
    # 保存到TXT文件
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(f"# {len(domains)}个5字母.com域名列表\n")
        f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"# 总计: {len(domains)} 个域名\n")
        f.write(f"# 基于高成功率模式生成，包含已验证的可注册域名\n")
        f.write(f"# 注意: 建议在注册前进行最终验证\n\n")
        
        for i, domain in enumerate(domains, 1):
            f.write(f"{i:5d}. {domain}.com\n")
    
    # 保存到CSV文件
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['序号', '域名', '完整域名', '特征', '生成时间'])
        for i, domain in enumerate(domains, 1):
            # 分析域名特征
            features = []
            if 'q' in domain and 'qu' not in domain:
                features.append('Q系列')
            if 'x' in domain or 'z' in domain:
                features.append('X/Z系列')
            if any(domain.count(c) >= 2 for c in domain):
                features.append('重复字母')
            consonant_count = sum(1 for c in domain if c not in 'aeiou')
            if consonant_count >= 4:
                features.append('辅音密集')
            if any(c in domain for c in 'jkvw'):
                features.append('J/K/V/W系列')
            
            feature_str = ', '.join(features) if features else '随机'
            writer.writerow([i, domain, f"{domain}.com", feature_str, datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    
    return txt_file, csv_file

def analyze_domains(domains):
    """分析域名特征分布"""
    print(f"\n=== 域名特征分析 ===")
    print(f"总域名数量: {len(domains)}")
    
    q_domains = [d for d in domains if 'q' in d and 'qu' not in d]
    print(f"Q系列域名: {len(q_domains)} 个")
    
    xz_domains = [d for d in domains if 'x' in d or 'z' in d]
    print(f"X/Z系列域名: {len(xz_domains)} 个")
    
    repeat_domains = [d for d in domains if any(d.count(c) >= 2 for c in d)]
    print(f"重复字母域名: {len(repeat_domains)} 个")
    
    consonant_heavy = [d for d in domains if sum(1 for c in d if c not in 'aeiou') >= 4]
    print(f"辅音密集域名: {len(consonant_heavy)} 个")
    
    jkvw_domains = [d for d in domains if any(c in d for c in 'jkvw')]
    print(f"J/K/V/W系列域名: {len(jkvw_domains)} 个")
    
    # 显示一些示例
    print(f"\nQ系列示例: {q_domains[:5]}")
    print(f"X/Z系列示例: {xz_domains[:5]}")
    print(f"重复字母示例: {repeat_domains[:5]}")

def main():
    print("=== 生成10000个5字母.com域名 ===")
    
    # 生成域名
    print("开始生成域名...")
    domains = generate_massive_domains(10000)
    
    print(f"\n生成完成！总共 {len(domains)} 个域名")
    
    # 分析域名特征
    analyze_domains(domains)
    
    # 保存到文件
    print("\n保存域名到文件...")
    txt_file, csv_file = save_domains_to_files(domains)
    
    print(f"\n域名已保存到:")
    print(f"  TXT文件: {txt_file}")
    print(f"  CSV文件: {csv_file}")
    
    # 显示前50个域名作为示例
    print(f"\n前50个域名示例:")
    for i, domain in enumerate(domains[:50], 1):
        print(f"  {i:2d}. {domain}.com")
    
    print(f"\n注意: 这些域名基于高成功率模式生成，包含已验证的可注册域名。")
    print(f"建议使用专业域名注册商进行最终验证。")

if __name__ == "__main__":
    main()
