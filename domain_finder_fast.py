#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速5字母.com域名查找工具 - 优化版
使用多线程和DNS查询提高速度
"""

import socket
import itertools
import random
import time
import csv
import json
from datetime import datetime
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

class FastDomainFinder:
    def __init__(self, target_count=1000, max_workers=20):
        self.target_count = target_count
        self.max_workers = max_workers
        self.found_domains = []
        self.checked_count = 0
        self.start_time = datetime.now()
        self.results_file = f"available_domains_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.csv_file = f"available_domains_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.progress_file = "domain_finder_fast_progress.json"
        self.lock = threading.Lock()
        
    def generate_5_letter_combinations(self):
        """生成所有5个小写字母的组合"""
        letters = 'abcdefghijklmnopqrstuvwxyz'
        combinations = [''.join(combo) for combo in itertools.product(letters, repeat=5)]
        random.shuffle(combinations)  # 随机打乱顺序
        return combinations
    
    def is_domain_available_dns(self, domain):
        """使用DNS查询检查域名是否可注册（更快）"""
        try:
            # 尝试解析域名的A记录
            socket.gethostbyname(domain)
            return False  # 如果能解析，说明域名已被注册
        except socket.gaierror:
            # 如果解析失败，可能域名未注册
            try:
                # 再尝试解析MX记录
                import dns.resolver
                dns.resolver.resolve(domain, 'MX')
                return False  # 有MX记录，域名已注册
            except:
                return True  # 没有任何记录，可能未注册
        except Exception:
            return False  # 其他错误，保守判断为已注册
    
    def is_domain_available_simple(self, domain):
        """简单的域名可用性检查"""
        try:
            # 尝试连接到域名的80端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((domain, 80))
            sock.close()
            return result != 0  # 如果连接失败，可能域名未注册
        except:
            return True  # 连接异常，可能未注册
    
    def check_domain(self, combo):
        """检查单个域名"""
        domain = f"{combo}.com"
        
        # 使用简单的DNS检查
        if self.is_domain_available_simple(domain):
            with self.lock:
                self.found_domains.append(combo)
                print(f"\n✅ 找到可注册域名: {domain} (总计: {len(self.found_domains)})")
                return True
        
        return False
    
    def save_progress(self):
        """保存进度"""
        progress_data = {
            'found_domains': self.found_domains,
            'checked_count': self.checked_count,
            'start_time': self.start_time.isoformat(),
            'target_count': self.target_count
        }
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
    
    def load_progress(self):
        """加载之前的进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    self.found_domains = progress_data.get('found_domains', [])
                    self.checked_count = progress_data.get('checked_count', 0)
                    self.target_count = progress_data.get('target_count', 1000)
                    print(f"加载进度：已找到 {len(self.found_domains)} 个域名，已检查 {self.checked_count} 个")
                    return True
            except Exception as e:
                print(f"加载进度失败: {e}")
        return False
    
    def save_results(self):
        """保存结果到文件"""
        # 保存到TXT文件
        with open(self.results_file, 'w', encoding='utf-8') as f:
            f.write(f"# 可注册的5字母.com域名列表\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 总计: {len(self.found_domains)} 个域名\n\n")
            for domain in self.found_domains:
                f.write(f"{domain}.com\n")
        
        # 保存到CSV文件
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['域名', '完整域名', '检查时间'])
            for domain in self.found_domains:
                writer.writerow([domain, f"{domain}.com", datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    
    def print_progress(self):
        """显示进度信息"""
        elapsed = datetime.now() - self.start_time
        found_count = len(self.found_domains)
        if self.checked_count > 0:
            success_rate = (found_count / self.checked_count) * 100
            speed = self.checked_count / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0
        else:
            success_rate = 0
            speed = 0
        
        print(f"\r进度: 已找到 {found_count}/{self.target_count} | "
              f"已检查 {self.checked_count} | "
              f"成功率 {success_rate:.2f}% | "
              f"速度 {speed:.1f}/秒 | "
              f"用时 {elapsed}", end='', flush=True)
    
    def find_domains(self):
        """主要的域名查找函数 - 多线程版本"""
        print("开始快速查找可注册的5字母.com域名...")
        print(f"目标: {self.target_count} 个域名，使用 {self.max_workers} 个线程")
        
        # 尝试加载之前的进度
        self.load_progress()
        
        if len(self.found_domains) >= self.target_count:
            print(f"\n已达到目标！找到 {len(self.found_domains)} 个可注册域名")
            self.save_results()
            return
        
        # 生成域名组合
        print("生成域名组合中...")
        combinations = self.generate_5_letter_combinations()
        print(f"总共有 {len(combinations)} 种组合")
        
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_combo = {}
                combo_iter = iter(combinations)
                
                # 初始提交一批任务
                for _ in range(self.max_workers * 2):
                    try:
                        combo = next(combo_iter)
                        future = executor.submit(self.check_domain, combo)
                        future_to_combo[future] = combo
                    except StopIteration:
                        break
                
                # 处理完成的任务并提交新任务
                for future in as_completed(future_to_combo):
                    if len(self.found_domains) >= self.target_count:
                        break
                    
                    combo = future_to_combo[future]
                    self.checked_count += 1
                    
                    # 每检查50个域名显示一次进度
                    if self.checked_count % 50 == 0:
                        self.print_progress()
                        self.save_progress()
                    
                    # 提交新任务
                    try:
                        new_combo = next(combo_iter)
                        new_future = executor.submit(self.check_domain, new_combo)
                        future_to_combo[new_future] = new_combo
                    except StopIteration:
                        pass
                        
        except KeyboardInterrupt:
            print(f"\n\n用户中断，已找到 {len(self.found_domains)} 个域名")
        
        print(f"\n\n查找完成！")
        print(f"总共找到 {len(self.found_domains)} 个可注册的5字母.com域名")
        print(f"检查了 {self.checked_count} 个域名")
        
        # 保存结果
        self.save_results()
        print(f"结果已保存到: {self.results_file} 和 {self.csv_file}")

def main():
    """主函数"""
    print("=== 快速5字母.com域名查找工具 ===")
    
    # 获取目标数量
    target = 1000
    if len(sys.argv) > 1:
        try:
            target = int(sys.argv[1])
        except ValueError:
            print("无效的目标数量，使用默认值1000")
    
    finder = FastDomainFinder(target_count=target, max_workers=50)
    finder.find_domains()

if __name__ == "__main__":
    main()
